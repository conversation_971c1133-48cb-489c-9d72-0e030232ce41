@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .message-bubble {
    @apply max-w-xs lg:max-w-md px-4 py-2 rounded-2xl break-words;
  }

  .message-sent {
    @apply bg-primary-500 text-white ml-auto rounded-br-md;
  }

  .message-received {
    @apply bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 mr-auto rounded-bl-md;
  }

  .user-avatar {
    @apply w-10 h-10 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white font-semibold text-sm;
  }

  .online-indicator {
    @apply w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800;
  }

  .typing-dots {
    @apply flex space-x-1;
  }

  .typing-dot {
    @apply w-2 h-2 bg-gray-400 rounded-full animate-bounce;
  }

  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }

  /* Dark mode scrollbar */
  .dark .scrollbar-thin {
    scrollbar-color: rgb(75 85 99) transparent;
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(75 85 99);
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
}
