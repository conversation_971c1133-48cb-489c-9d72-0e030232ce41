import React from 'react';
import { useChat } from '../../context/ChatContext';
import ChatHeader from '../Chat/ChatHeader';
import MessageList from '../Chat/MessageList';
import MessageInput from '../Chat/MessageInput';
import EmptyState from '../UI/EmptyState';

const ChatArea: React.FC = () => {
  const { state } = useChat();

  const activeConversation = state.conversations.find(
    conv => conv.id === state.activeConversationId
  );

  if (!activeConversation) {
    return <EmptyState />;
  }

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-800">
      {/* Chat Header */}
      <ChatHeader conversation={activeConversation} />

      {/* Messages Area */}
      <div className="flex-1 overflow-hidden">
        <MessageList conversation={activeConversation} />
      </div>

      {/* Message Input */}
      <MessageInput conversationId={activeConversation.id} />
    </div>
  );
};

export default ChatArea;
