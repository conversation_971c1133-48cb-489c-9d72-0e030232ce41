# Modern Chat Application

A modern, responsive chat application built with React, TypeScript, Vite, and Tailwind CSS. This is a fully functional frontend mockup with realistic sample data, focusing on user interface and user experience.

## 🚀 Features

### Core Features
- **Real-time chat interface** with message bubbles (sent/received styling)
- **User list/contacts sidebar** showing online status
- **Message input field** with send button and emoji picker
- **Chat header** displaying current conversation partner
- **Timestamp display** for messages with smart formatting
- **Typing indicators** with animated dots
- **Message status indicators** (sent, delivered, read)

### UI/UX Features
- **Responsive design** that works on desktop and mobile
- **Dark/light theme toggle** with smooth transitions
- **Modern CSS** using Tailwind CSS with custom components
- **Smooth animations** and transitions for better UX
- **Custom scrollbars** for better visual appeal
- **Mobile-first responsive navigation**

### Advanced Features
- **Search functionality** across conversations and messages
- **Group chat support** with participant indicators
- **Emoji picker** with categorized emojis
- **File sharing mockup** (UI ready)
- **Voice message indicators** (UI ready)
- **Auto-response simulation** for demo purposes
- **Loading states and empty states**

## 🛠️ Tech Stack

- **React 19** - Frontend framework
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **Lucide React** - Modern icon library
- **Date-fns** - Date formatting utilities

## 📱 Screenshots

The application features:
- Clean, modern interface similar to popular chat apps (WhatsApp, Telegram, Discord)
- Proper color contrast and accessibility considerations
- Intuitive navigation and user interactions
- Realistic mock data with multiple users and conversations

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd chat-app
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5174`

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🎨 Customization

### Theme
The application supports both light and dark themes. You can toggle between themes using the button in the sidebar header.

### Mock Data
Sample data is located in `src/data/mockData.ts`. You can modify:
- Users and their online status
- Conversations and messages
- Group chat configurations

### Styling
The application uses Tailwind CSS with custom components defined in `src/index.css`. Key classes:
- `.message-bubble` - Base message styling
- `.message-sent` - Sent message styling
- `.message-received` - Received message styling
- `.user-avatar` - User avatar styling

## 📁 Project Structure

```
src/
├── components/
│   ├── Chat/           # Chat-related components
│   ├── Layout/         # Layout components
│   └── UI/             # Reusable UI components
├── context/            # React context for state management
├── data/               # Mock data
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
└── App.tsx             # Main application component
```

## 🔧 Key Components

- **ChatLayout** - Main layout with responsive sidebar
- **ConversationList** - List of conversations with search
- **MessageList** - Scrollable message history
- **MessageInput** - Input field with emoji picker
- **TypingIndicator** - Animated typing indicator

## 🌟 Demo Features

- **Auto-responses** - The app simulates responses from other users
- **Typing simulation** - Shows typing indicators before responses
- **Realistic timestamps** - Smart date/time formatting
- **Online status** - Visual indicators for user availability

## 🚀 Future Enhancements

This mockup is ready for backend integration. Potential enhancements:
- WebSocket integration for real-time messaging
- File upload and sharing
- Voice/video calling
- Push notifications
- Message encryption
- User authentication

## 📄 License

This project is open source and available under the [MIT License](LICENSE).
