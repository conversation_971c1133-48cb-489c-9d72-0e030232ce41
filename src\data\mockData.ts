import { User, Conversation, Message } from '../types';

export const currentUser: User = {
  id: 'user-1',
  name: 'You',
  status: 'online',
};

export const users: User[] = [
  {
    id: 'user-2',
    name: '<PERSON>',
    status: 'online',
  },
  {
    id: 'user-3',
    name: '<PERSON>',
    status: 'away',
    lastSeen: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
  },
  {
    id: 'user-4',
    name: '<PERSON>',
    status: 'offline',
    lastSeen: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
  },
  {
    id: 'user-5',
    name: '<PERSON>',
    status: 'online',
  },
  {
    id: 'user-6',
    name: '<PERSON>',
    status: 'away',
    lastSeen: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
  },
  {
    id: 'user-7',
    name: '<PERSON>',
    status: 'online',
  },
  {
    id: 'user-8',
    name: '<PERSON>',
    status: 'offline',
    lastSeen: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
  },
];

const generateMessages = (participants: string[], count: number = 10): Message[] => {
  const messages: Message[] = [];
  const messageTemplates = [
    "Hey there! How's it going?",
    "Did you see the latest update?",
    "I'm working on the new project",
    "Let's catch up soon!",
    "Thanks for your help earlier",
    "The meeting went really well",
    "I'll send you the files later",
    "Great job on the presentation!",
    "Are you free this weekend?",
    "Just finished the report",
    "Looking forward to our collaboration",
    "The weather is amazing today!",
    "I found a great restaurant",
    "Can you review this document?",
    "Happy birthday! 🎉",
    "The event was fantastic",
    "I'll be there in 10 minutes",
    "Thanks for the recommendation",
    "How was your vacation?",
    "Let's schedule a call",
  ];

  for (let i = 0; i < count; i++) {
    const senderId = participants[Math.floor(Math.random() * participants.length)];
    const timestamp = new Date(Date.now() - Math.random() * 1000 * 60 * 60 * 24 * 7); // Random time in last week
    
    messages.push({
      id: `msg-${Date.now()}-${i}`,
      senderId,
      content: messageTemplates[Math.floor(Math.random() * messageTemplates.length)],
      timestamp,
      status: senderId === currentUser.id ? 'read' : 'delivered',
      type: 'text',
    });
  }

  return messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
};

export const conversations: Conversation[] = [
  {
    id: 'conv-1',
    participants: [currentUser, users[0]], // Alice
    messages: generateMessages([currentUser.id, users[0].id], 15),
    unreadCount: 2,
    isGroup: false,
    isTyping: [],
  },
  {
    id: 'conv-2',
    participants: [currentUser, users[1]], // Bob
    messages: generateMessages([currentUser.id, users[1].id], 8),
    unreadCount: 0,
    isGroup: false,
    isTyping: [],
  },
  {
    id: 'conv-3',
    participants: [currentUser, users[2]], // Carol
    messages: generateMessages([currentUser.id, users[2].id], 12),
    unreadCount: 1,
    isGroup: false,
    isTyping: [],
  },
  {
    id: 'conv-4',
    participants: [currentUser, users[3], users[4], users[5]], // Group chat
    messages: generateMessages([currentUser.id, users[3].id, users[4].id, users[5].id], 20),
    unreadCount: 5,
    isGroup: true,
    groupName: 'Project Team',
    isTyping: [],
  },
  {
    id: 'conv-5',
    participants: [currentUser, users[6]], // Grace
    messages: generateMessages([currentUser.id, users[6].id], 6),
    unreadCount: 0,
    isGroup: false,
    isTyping: [],
  },
];

// Add lastMessage to conversations
conversations.forEach(conv => {
  if (conv.messages.length > 0) {
    conv.lastMessage = conv.messages[conv.messages.length - 1];
  }
});

export const getUserById = (id: string): User | undefined => {
  if (id === currentUser.id) return currentUser;
  return users.find(user => user.id === id);
};

export const getConversationById = (id: string): Conversation | undefined => {
  return conversations.find(conv => conv.id === id);
};
