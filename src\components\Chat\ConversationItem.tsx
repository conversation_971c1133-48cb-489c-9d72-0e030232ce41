import React from 'react';
import { Conversation } from '../../types';
import { useChat } from '../../context/ChatContext';
import { 
  getConversationName, 
  getConversationAvatar, 
  formatMessageTime, 
  truncateText,
  getInitials,
  generateAvatarColor
} from '../../utils/helpers';
import { Users } from 'lucide-react';

interface ConversationItemProps {
  conversation: Conversation;
}

const ConversationItem: React.FC<ConversationItemProps> = ({ conversation }) => {
  const { state, setActiveConversation } = useChat();
  
  const isActive = state.activeConversationId === conversation.id;
  const conversationName = getConversationName(conversation, state.currentUser.id);
  const conversationAvatar = getConversationAvatar(conversation, state.currentUser.id);
  
  const otherUser = conversation.participants.find(p => p.id !== state.currentUser.id);
  const isOnline = otherUser?.status === 'online';
  
  const lastMessageText = conversation.lastMessage 
    ? truncateText(conversation.lastMessage.content, 50)
    : 'No messages yet';
    
  const lastMessageTime = conversation.lastMessage 
    ? formatMessageTime(conversation.lastMessage.timestamp)
    : '';

  const handleClick = () => {
    setActiveConversation(conversation.id);
  };

  const avatarColor = generateAvatarColor(conversationName);

  return (
    <div
      onClick={handleClick}
      className={`p-4 cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-gray-700 ${
        isActive ? 'bg-primary-50 dark:bg-primary-900/20 border-r-2 border-primary-500' : ''
      }`}
    >
      <div className="flex items-center gap-3">
        {/* Avatar */}
        <div className="relative flex-shrink-0">
          {conversationAvatar ? (
            <img
              src={conversationAvatar}
              alt={conversationName}
              className="w-12 h-12 rounded-full object-cover"
            />
          ) : (
            <div className={`w-12 h-12 rounded-full ${avatarColor} flex items-center justify-center text-white font-semibold`}>
              {conversation.isGroup ? (
                <Users className="w-6 h-6" />
              ) : (
                getInitials(conversationName)
              )}
            </div>
          )}
          
          {/* Online indicator for individual chats */}
          {!conversation.isGroup && isOnline && (
            <div className="absolute -bottom-1 -right-1 online-indicator"></div>
          )}
          
          {/* Unread count */}
          {conversation.unreadCount > 0 && (
            <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
              {conversation.unreadCount > 9 ? '9+' : conversation.unreadCount}
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h3 className={`text-sm font-medium truncate ${
              isActive 
                ? 'text-primary-600 dark:text-primary-400' 
                : 'text-gray-900 dark:text-white'
            }`}>
              {conversationName}
            </h3>
            {lastMessageTime && (
              <span className="text-xs text-gray-500 dark:text-gray-400 flex-shrink-0 ml-2">
                {lastMessageTime}
              </span>
            )}
          </div>
          
          <div className="flex items-center justify-between">
            <p className={`text-sm truncate ${
              conversation.unreadCount > 0 
                ? 'text-gray-900 dark:text-white font-medium' 
                : 'text-gray-500 dark:text-gray-400'
            }`}>
              {conversation.isTyping && conversation.isTyping.length > 0 ? (
                <span className="text-primary-500 italic">
                  {conversation.isTyping.length === 1 ? 'Typing...' : 'Multiple people typing...'}
                </span>
              ) : (
                lastMessageText
              )}
            </p>
          </div>
          
          {/* Group chat participants count */}
          {conversation.isGroup && (
            <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
              {conversation.participants.length} participants
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ConversationItem;
