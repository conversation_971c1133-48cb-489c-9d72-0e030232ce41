import React from 'react';
import { Conversation } from '../../types';
import { getUserById } from '../../data/mockData';
import { getInitials, generateAvatarColor } from '../../utils/helpers';

interface TypingIndicatorProps {
  conversation: Conversation;
  currentUserId: string;
}

const TypingIndicator: React.FC<TypingIndicatorProps> = ({ conversation, currentUserId }) => {
  if (!conversation.isTyping || conversation.isTyping.length === 0) {
    return null;
  }

  const typingUsers = conversation.isTyping
    .map(userId => getUserById(userId))
    .filter(user => user && user.id !== currentUserId);

  if (typingUsers.length === 0) {
    return null;
  }

  const firstUser = typingUsers[0];
  const avatarColor = generateAvatarColor(firstUser?.name || '');

  const getTypingText = () => {
    if (typingUsers.length === 1) {
      return conversation.isGroup ? `${firstUser?.name} is typing...` : 'typing...';
    } else if (typingUsers.length === 2) {
      return `${firstUser?.name} and ${typingUsers[1]?.name} are typing...`;
    } else {
      return `${firstUser?.name} and ${typingUsers.length - 1} others are typing...`;
    }
  };

  return (
    <div className="flex gap-3">
      {/* Avatar for group chats */}
      {conversation.isGroup && firstUser && (
        <div className={`w-8 h-8 rounded-full ${avatarColor} flex items-center justify-center text-white font-semibold text-xs flex-shrink-0`}>
          {getInitials(firstUser.name)}
        </div>
      )}
      
      {/* Typing bubble */}
      <div className="flex flex-col items-start max-w-xs lg:max-w-md">
        <div className="message-bubble message-received animate-fade-in">
          <div className="flex items-center gap-1">
            <span className="text-sm text-gray-500 dark:text-gray-400 italic">
              {getTypingText()}
            </span>
            <div className="typing-dots ml-2">
              <div className="typing-dot" style={{ animationDelay: '0ms' }}></div>
              <div className="typing-dot" style={{ animationDelay: '150ms' }}></div>
              <div className="typing-dot" style={{ animationDelay: '300ms' }}></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TypingIndicator;
