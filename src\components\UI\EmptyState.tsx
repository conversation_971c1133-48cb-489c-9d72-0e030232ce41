import React from 'react';
import { MessageCircle, Users, Zap } from 'lucide-react';

const EmptyState: React.FC = () => {
  return (
    <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="text-center max-w-md mx-auto px-6">
        {/* Icon */}
        <div className="w-24 h-24 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center mx-auto mb-6">
          <MessageCircle className="w-12 h-12 text-primary-500" />
        </div>
        
        {/* Title */}
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-3">
          Welcome to Chat App
        </h2>
        
        {/* Description */}
        <p className="text-gray-600 dark:text-gray-400 mb-8 leading-relaxed">
          Select a conversation from the sidebar to start chatting, or create a new conversation to connect with your friends and colleagues.
        </p>
        
        {/* Features */}
        <div className="space-y-4 text-left">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <Zap className="w-4 h-4 text-green-600 dark:text-green-400" />
            </div>
            <span className="text-sm text-gray-700 dark:text-gray-300">
              Real-time messaging with instant delivery
            </span>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <Users className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
            <span className="text-sm text-gray-700 dark:text-gray-300">
              Group chats and individual conversations
            </span>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <MessageCircle className="w-4 h-4 text-purple-600 dark:text-purple-400" />
            </div>
            <span className="text-sm text-gray-700 dark:text-gray-300">
              Rich messaging with emojis and file sharing
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmptyState;
