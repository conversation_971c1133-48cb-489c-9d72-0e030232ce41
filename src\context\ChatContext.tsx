import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { ChatState, Conversation, Message } from '../types';
import { currentUser, conversations as initialConversations } from '../data/mockData';

interface ChatContextType {
  state: ChatState;
  dispatch: React.Dispatch<ChatAction>;
  sendMessage: (conversationId: string, content: string) => void;
  markAsRead: (conversationId: string) => void;
  setActiveConversation: (conversationId: string | null) => void;
  toggleTheme: () => void;
  setSearchQuery: (query: string) => void;
  startTyping: (conversationId: string, userId: string) => void;
  stopTyping: (conversationId: string, userId: string) => void;
}

type ChatAction =
  | { type: 'SEND_MESSAGE'; payload: { conversationId: string; message: Message } }
  | { type: 'MARK_AS_READ'; payload: { conversationId: string } }
  | { type: 'SET_ACTIVE_CONVERSATION'; payload: { conversationId: string | null } }
  | { type: 'TOGGLE_THEME' }
  | { type: 'SET_SEARCH_QUERY'; payload: { query: string } }
  | { type: 'START_TYPING'; payload: { conversationId: string; userId: string } }
  | { type: 'STOP_TYPING'; payload: { conversationId: string; userId: string } };

const initialState: ChatState = {
  currentUser,
  conversations: initialConversations,
  activeConversationId: initialConversations[0]?.id || null,
  theme: 'light',
  searchQuery: '',
};

const chatReducer = (state: ChatState, action: ChatAction): ChatState => {
  switch (action.type) {
    case 'SEND_MESSAGE':
      return {
        ...state,
        conversations: state.conversations.map(conv =>
          conv.id === action.payload.conversationId
            ? {
                ...conv,
                messages: [...conv.messages, action.payload.message],
                lastMessage: action.payload.message,
              }
            : conv
        ),
      };

    case 'MARK_AS_READ':
      return {
        ...state,
        conversations: state.conversations.map(conv =>
          conv.id === action.payload.conversationId
            ? { ...conv, unreadCount: 0 }
            : conv
        ),
      };

    case 'SET_ACTIVE_CONVERSATION':
      return {
        ...state,
        activeConversationId: action.payload.conversationId,
      };

    case 'TOGGLE_THEME':
      return {
        ...state,
        theme: state.theme === 'light' ? 'dark' : 'light',
      };

    case 'SET_SEARCH_QUERY':
      return {
        ...state,
        searchQuery: action.payload.query,
      };

    case 'START_TYPING':
      return {
        ...state,
        conversations: state.conversations.map(conv =>
          conv.id === action.payload.conversationId
            ? {
                ...conv,
                isTyping: [...(conv.isTyping || []), action.payload.userId].filter(
                  (id, index, arr) => arr.indexOf(id) === index
                ),
              }
            : conv
        ),
      };

    case 'STOP_TYPING':
      return {
        ...state,
        conversations: state.conversations.map(conv =>
          conv.id === action.payload.conversationId
            ? {
                ...conv,
                isTyping: (conv.isTyping || []).filter(id => id !== action.payload.userId),
              }
            : conv
        ),
      };

    default:
      return state;
  }
};

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export const ChatProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(chatReducer, initialState);

  const sendMessage = (conversationId: string, content: string) => {
    const message: Message = {
      id: `msg-${Date.now()}`,
      senderId: state.currentUser.id,
      content,
      timestamp: new Date(),
      status: 'sent',
      type: 'text',
    };

    dispatch({
      type: 'SEND_MESSAGE',
      payload: { conversationId, message },
    });

    // Simulate message delivery after a short delay
    setTimeout(() => {
      // In a real app, this would be handled by the backend
      // For demo purposes, simulate someone typing back occasionally
      const conversation = state.conversations.find(c => c.id === conversationId);
      if (conversation && Math.random() > 0.7) {
        const otherParticipants = conversation.participants.filter(p => p.id !== state.currentUser.id);
        if (otherParticipants.length > 0) {
          const randomUser = otherParticipants[Math.floor(Math.random() * otherParticipants.length)];

          // Start typing
          startTyping(conversationId, randomUser.id);

          // Stop typing and send a response after 2-4 seconds
          setTimeout(() => {
            stopTyping(conversationId, randomUser.id);

            const responses = [
              "That's interesting!",
              "I agree with you",
              "Thanks for sharing",
              "Let me think about that",
              "Good point!",
              "I'll get back to you on this",
              "Sounds good to me",
              "That makes sense"
            ];

            const responseMessage: Message = {
              id: `msg-${Date.now()}-response`,
              senderId: randomUser.id,
              content: responses[Math.floor(Math.random() * responses.length)],
              timestamp: new Date(),
              status: 'delivered',
              type: 'text',
            };

            dispatch({
              type: 'SEND_MESSAGE',
              payload: { conversationId, message: responseMessage },
            });
          }, 2000 + Math.random() * 2000);
        }
      }
    }, 1000);
  };

  const markAsRead = (conversationId: string) => {
    dispatch({ type: 'MARK_AS_READ', payload: { conversationId } });
  };

  const setActiveConversation = (conversationId: string | null) => {
    dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: { conversationId } });
    if (conversationId) {
      markAsRead(conversationId);
    }
  };

  const toggleTheme = () => {
    dispatch({ type: 'TOGGLE_THEME' });
  };

  const setSearchQuery = (query: string) => {
    dispatch({ type: 'SET_SEARCH_QUERY', payload: { query } });
  };

  const startTyping = (conversationId: string, userId: string) => {
    dispatch({ type: 'START_TYPING', payload: { conversationId, userId } });
  };

  const stopTyping = (conversationId: string, userId: string) => {
    dispatch({ type: 'STOP_TYPING', payload: { conversationId, userId } });
  };

  return (
    <ChatContext.Provider
      value={{
        state,
        dispatch,
        sendMessage,
        markAsRead,
        setActiveConversation,
        toggleTheme,
        setSearchQuery,
        startTyping,
        stopTyping,
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};

export const useChat = () => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};
