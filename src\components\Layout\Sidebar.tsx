import React, { useState } from 'react';
import { Search, Plus } from 'lucide-react';
import { useChat } from '../../context/ChatContext';
import ConversationList from '../Chat/ConversationList';
import { searchConversations } from '../../utils/helpers';

const Sidebar: React.FC = () => {
  const { state, setSearchQuery } = useChat();
  const [localSearchQuery, setLocalSearchQuery] = useState('');

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setLocalSearchQuery(query);
    setSearchQuery(query);
  };

  const filteredConversations = searchConversations(
    state.conversations,
    state.searchQuery,
    state.currentUser.id
  );

  return (
    <div className="flex flex-col h-full">
      {/* Search Bar */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search conversations..."
            value={localSearchQuery}
            onChange={handleSearchChange}
            className="w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-700 border border-transparent rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          />
        </div>
      </div>

      {/* New Chat Button */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <button className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors">
          <Plus className="w-4 h-4" />
          New Chat
        </button>
      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto scrollbar-thin">
        <ConversationList conversations={filteredConversations} />
      </div>

      {/* User Profile Section */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-3">
          <div className="relative">
            <div className="user-avatar">
              {state.currentUser.name.charAt(0)}
            </div>
            <div className="absolute -bottom-1 -right-1 online-indicator"></div>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {state.currentUser.name}
            </p>
            <p className="text-xs text-green-500 capitalize">
              {state.currentUser.status}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
