import { format, isToday, isYesterday, formatDistanceToNow } from 'date-fns';
import { User, Conversation } from '../types';

export const formatMessageTime = (date: Date): string => {
  if (isToday(date)) {
    return format(date, 'HH:mm');
  } else if (isYesterday(date)) {
    return 'Yesterday';
  } else {
    return format(date, 'MMM dd');
  }
};

export const formatLastSeen = (date: Date): string => {
  return `Last seen ${formatDistanceToNow(date, { addSuffix: true })}`;
};

export const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

export const getConversationName = (conversation: Conversation, currentUserId: string): string => {
  if (conversation.isGroup) {
    return conversation.groupName || 'Group Chat';
  }
  
  const otherUser = conversation.participants.find(p => p.id !== currentUserId);
  return otherUser?.name || 'Unknown User';
};

export const getConversationAvatar = (conversation: Conversation, currentUserId: string): string => {
  if (conversation.isGroup) {
    return conversation.groupAvatar || '';
  }
  
  const otherUser = conversation.participants.find(p => p.id !== currentUserId);
  return otherUser?.avatar || '';
};

export const getOnlineParticipants = (conversation: Conversation, currentUserId: string): User[] => {
  return conversation.participants.filter(
    p => p.id !== currentUserId && p.status === 'online'
  );
};

export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
};

export const searchConversations = (conversations: Conversation[], query: string, currentUserId: string): Conversation[] => {
  if (!query.trim()) return conversations;
  
  const lowercaseQuery = query.toLowerCase();
  
  return conversations.filter(conversation => {
    // Search in conversation name
    const conversationName = getConversationName(conversation, currentUserId).toLowerCase();
    if (conversationName.includes(lowercaseQuery)) return true;
    
    // Search in participant names
    const participantMatch = conversation.participants.some(participant =>
      participant.name.toLowerCase().includes(lowercaseQuery)
    );
    if (participantMatch) return true;
    
    // Search in recent messages
    const messageMatch = conversation.messages
      .slice(-10) // Only search last 10 messages for performance
      .some(message => message.content.toLowerCase().includes(lowercaseQuery));
    
    return messageMatch;
  });
};

export const generateAvatarColor = (name: string): string => {
  const colors = [
    'bg-red-500',
    'bg-blue-500',
    'bg-green-500',
    'bg-yellow-500',
    'bg-purple-500',
    'bg-pink-500',
    'bg-indigo-500',
    'bg-teal-500',
    'bg-orange-500',
    'bg-cyan-500',
  ];
  
  const hash = name.split('').reduce((acc, char) => {
    return char.charCodeAt(0) + ((acc << 5) - acc);
  }, 0);
  
  return colors[Math.abs(hash) % colors.length];
};

export const playNotificationSound = () => {
  // In a real app, you would play an actual sound file
  // For now, we'll just use the browser's default notification
  if ('Notification' in window && Notification.permission === 'granted') {
    // This is just a placeholder - in a real app you'd play an audio file
    console.log('🔔 New message notification');
  }
};

export const requestNotificationPermission = async (): Promise<boolean> => {
  if (!('Notification' in window)) {
    return false;
  }
  
  if (Notification.permission === 'granted') {
    return true;
  }
  
  if (Notification.permission === 'denied') {
    return false;
  }
  
  const permission = await Notification.requestPermission();
  return permission === 'granted';
};
