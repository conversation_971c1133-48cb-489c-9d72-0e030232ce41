import React from 'react';
import { Conversation } from '../../types';
import { useChat } from '../../context/ChatContext';
import { 
  getConversationName, 
  getConversationAvatar, 
  getInitials,
  generateAvatarColor,
  getOnlineParticipants
} from '../../utils/helpers';
import { Phone, Video, MoreVertical, Users, Search } from 'lucide-react';

interface ChatHeaderProps {
  conversation: Conversation;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({ conversation }) => {
  const { state } = useChat();
  
  const conversationName = getConversationName(conversation, state.currentUser.id);
  const conversationAvatar = getConversationAvatar(conversation, state.currentUser.id);
  const avatarColor = generateAvatarColor(conversationName);
  
  const otherUser = conversation.participants.find(p => p.id !== state.currentUser.id);
  const onlineParticipants = getOnlineParticipants(conversation, state.currentUser.id);
  
  const getStatusText = () => {
    if (conversation.isGroup) {
      const onlineCount = onlineParticipants.length;
      if (onlineCount === 0) {
        return `${conversation.participants.length - 1} participants`;
      }
      return `${onlineCount} online, ${conversation.participants.length - 1} participants`;
    } else {
      if (conversation.isTyping && conversation.isTyping.length > 0) {
        return 'typing...';
      }
      return otherUser?.status || 'offline';
    }
  };

  const statusText = getStatusText();
  const isOnline = conversation.isGroup 
    ? onlineParticipants.length > 0 
    : otherUser?.status === 'online';

  return (
    <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
      <div className="flex items-center justify-between">
        {/* Left side - Avatar and info */}
        <div className="flex items-center gap-3">
          <div className="relative">
            {conversationAvatar ? (
              <img
                src={conversationAvatar}
                alt={conversationName}
                className="w-10 h-10 rounded-full object-cover"
              />
            ) : (
              <div className={`w-10 h-10 rounded-full ${avatarColor} flex items-center justify-center text-white font-semibold text-sm`}>
                {conversation.isGroup ? (
                  <Users className="w-5 h-5" />
                ) : (
                  getInitials(conversationName)
                )}
              </div>
            )}
            
            {/* Online indicator */}
            {isOnline && !conversation.isGroup && (
              <div className="absolute -bottom-1 -right-1 online-indicator"></div>
            )}
          </div>

          <div className="min-w-0">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
              {conversationName}
            </h2>
            <p className={`text-sm ${
              conversation.isTyping && conversation.isTyping.length > 0
                ? 'text-primary-500 italic'
                : isOnline
                ? 'text-green-500'
                : 'text-gray-500 dark:text-gray-400'
            }`}>
              {statusText}
            </p>
          </div>
        </div>

        {/* Right side - Action buttons */}
        <div className="flex items-center gap-2">
          <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
            <Search className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
          <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
            <Phone className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
          <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
            <Video className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
          <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
            <MoreVertical className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatHeader;
