import React from 'react';
import { Message, Conversation } from '../../types';
import { formatMessageTime, getInitials, generateAvatarColor } from '../../utils/helpers';
import { getUserById } from '../../data/mockData';
import { Check, CheckCheck, Clock } from 'lucide-react';

interface MessageBubbleProps {
  message: Message;
  isOwn: boolean;
  conversation: Conversation;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message, isOwn, conversation }) => {
  const sender = getUserById(message.senderId);
  const senderName = sender?.name || 'Unknown User';
  const avatarColor = generateAvatarColor(senderName);

  const getStatusIcon = () => {
    if (!isOwn) return null;
    
    switch (message.status) {
      case 'sent':
        return <Clock className="w-3 h-3 text-gray-400" />;
      case 'delivered':
        return <Check className="w-3 h-3 text-gray-400" />;
      case 'read':
        return <CheckCheck className="w-3 h-3 text-primary-500" />;
      default:
        return null;
    }
  };

  return (
    <div className={`flex gap-3 ${isOwn ? 'flex-row-reverse' : 'flex-row'}`}>
      {/* Avatar - only show for received messages in group chats */}
      {!isOwn && conversation.isGroup && (
        <div className={`w-8 h-8 rounded-full ${avatarColor} flex items-center justify-center text-white font-semibold text-xs flex-shrink-0`}>
          {getInitials(senderName)}
        </div>
      )}
      
      {/* Message content */}
      <div className={`flex flex-col ${isOwn ? 'items-end' : 'items-start'} max-w-xs lg:max-w-md`}>
        {/* Sender name for group chats */}
        {!isOwn && conversation.isGroup && (
          <span className="text-xs text-gray-500 dark:text-gray-400 mb-1 px-1">
            {senderName}
          </span>
        )}
        
        {/* Message bubble */}
        <div
          className={`message-bubble ${
            isOwn ? 'message-sent' : 'message-received'
          } animate-fade-in`}
        >
          <p className="text-sm leading-relaxed">{message.content}</p>
        </div>
        
        {/* Timestamp and status */}
        <div className={`flex items-center gap-1 mt-1 px-1 ${isOwn ? 'flex-row-reverse' : 'flex-row'}`}>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {formatMessageTime(message.timestamp)}
          </span>
          {getStatusIcon()}
        </div>
      </div>
    </div>
  );
};

export default MessageBubble;
