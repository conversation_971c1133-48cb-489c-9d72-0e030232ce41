import React, { useEffect, useRef } from 'react';
import { Conversation } from '../../types';
import { useChat } from '../../context/ChatContext';
import MessageBubble from './MessageBubble';
import TypingIndicator from './TypingIndicator';
import { format, isSameDay } from 'date-fns';

interface MessageListProps {
  conversation: Conversation;
}

const MessageList: React.FC<MessageListProps> = ({ conversation }) => {
  const { state } = useChat();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [conversation.messages]);

  const renderDateSeparator = (date: Date) => (
    <div className="flex items-center justify-center my-4">
      <div className="bg-gray-200 dark:bg-gray-700 px-3 py-1 rounded-full">
        <span className="text-xs text-gray-600 dark:text-gray-400 font-medium">
          {format(date, 'MMMM dd, yyyy')}
        </span>
      </div>
    </div>
  );

  const groupedMessages = conversation.messages.reduce((groups, message, index) => {
    const messageDate = message.timestamp;
    const prevMessage = conversation.messages[index - 1];
    
    // Add date separator if this is the first message or if the date changed
    if (!prevMessage || !isSameDay(prevMessage.timestamp, messageDate)) {
      groups.push({
        type: 'date',
        date: messageDate,
      });
    }
    
    groups.push({
      type: 'message',
      message,
    });
    
    return groups;
  }, [] as Array<{ type: 'date'; date: Date } | { type: 'message'; message: any }>);

  return (
    <div className="flex-1 overflow-y-auto scrollbar-thin p-4 space-y-4">
      {groupedMessages.map((item, index) => {
        if (item.type === 'date') {
          return (
            <div key={`date-${index}`}>
              {renderDateSeparator(item.date)}
            </div>
          );
        }
        
        return (
          <MessageBubble
            key={item.message.id}
            message={item.message}
            isOwn={item.message.senderId === state.currentUser.id}
            conversation={conversation}
          />
        );
      })}
      
      {/* Typing indicator */}
      {conversation.isTyping && conversation.isTyping.length > 0 && (
        <TypingIndicator 
          conversation={conversation}
          currentUserId={state.currentUser.id}
        />
      )}
      
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
