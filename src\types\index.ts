export interface User {
  id: string;
  name: string;
  avatar?: string;
  status: 'online' | 'offline' | 'away';
  lastSeen?: Date;
}

export interface Message {
  id: string;
  senderId: string;
  content: string;
  timestamp: Date;
  status: 'sent' | 'delivered' | 'read';
  type: 'text' | 'image' | 'file' | 'emoji';
  fileUrl?: string;
  fileName?: string;
  replyTo?: string;
}

export interface Conversation {
  id: string;
  participants: User[];
  messages: Message[];
  lastMessage?: Message;
  unreadCount: number;
  isGroup: boolean;
  groupName?: string;
  groupAvatar?: string;
  isTyping?: string[]; // user IDs who are typing
}

export interface ChatState {
  currentUser: User;
  conversations: Conversation[];
  activeConversationId: string | null;
  theme: 'light' | 'dark';
  searchQuery: string;
}

export type MessageStatus = 'sent' | 'delivered' | 'read';
export type UserStatus = 'online' | 'offline' | 'away';
export type MessageType = 'text' | 'image' | 'file' | 'emoji';
